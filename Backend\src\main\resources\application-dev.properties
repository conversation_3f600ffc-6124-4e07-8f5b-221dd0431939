# Development Profile Configuration
spring.profiles.active=dev

# Database Configuration (PostgreSQL for Development)
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=password

# JPA Configuration for Development
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging for Development
logging.level.com.spring.fit.backend=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE 