# Application Configuration
spring.application.name=backend
server.port=8080

# Database Configuration (PostgreSQL)
spring.datasource.url=********************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JWT Configuration
app.jwt.secret=your-super-secret-jwt-key-that-should-be-very-long-and-secure-in-production
app.jwt.expiration=86400000

# Logging Configuration
logging.level.com.spring.fit.backend=DEBUG
logging.level.org.springframework.security=DEBUG

# CORS Configuration
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
