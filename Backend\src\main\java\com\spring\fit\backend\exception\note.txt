src/main/java/com/spring/fit/backend/exception/
├── GlobalExceptionHandler.java      # Xử lý tất cả exceptions
├── ErrorResponse.java               # Response model cho errors
├── ValidationErrorResponse.java     # Response model cho validation errors
├── ResourceNotFoundException.java   # 404 - Không tìm thấy resource
├── BadRequestException.java         # 400 - Request không hợp lệ
├── UnauthorizedException.java       # 401 - Chưa xác thực
├── ForbiddenException.java          # 403 - Không có quyền truy cập
├── ValidationException.java         # 400 - Validation errors
├── ConflictException.java           # 409 - Conflict (duplicate)
├── InternalServerException.java     # 500 - Server error
└── ExceptionUtils.java              # Utility để tạo exceptions