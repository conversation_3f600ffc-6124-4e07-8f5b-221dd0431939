### Test Public Endpoints
GET http://localhost:8080/api/public/health
Content-Type: application/json

###

GET http://localhost:8080/api/public/info
Content-Type: application/json

###

### Test Registration
POST http://localhost:8080/api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Test",
  "lastName": "User"
}

###

### Test Login with Admin
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

###

### Test Login with User
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "username": "user",
  "password": "user123"
}

###

### Test Get Current User (Replace TOKEN with actual token from login response)
GET http://localhost:8080/api/auth/me
Authorization: Bearer TOKEN
Content-Type: application/json

###

### Test Admin Endpoint (Replace TOKEN with admin token)
GET http://localhost:8080/api/admin/users
Authorization: Bearer TOKEN
Content-Type: application/json

###

### Test User Endpoint (Replace TOKEN with user token)
GET http://localhost:8080/api/auth/user
Authorization: Bearer TOKEN
Content-Type: application/json

###

### Test Admin Only Endpoint (Replace TOKEN with admin token)
GET http://localhost:8080/api/auth/admin
Authorization: Bearer TOKEN
Content-Type: application/json 