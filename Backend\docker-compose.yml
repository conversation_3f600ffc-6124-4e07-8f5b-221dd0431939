version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: fit_postgres
    environment:
      POSTGRES_DB: fit_backend_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - fit_network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: fit_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8081:80"
    depends_on:
      - postgres
    networks:
      - fit_network

volumes:
  postgres_data:

networks:
  fit_network:
    driver: bridge 