# Production Profile Configuration
spring.profiles.active=prod

# Database Configuration (PostgreSQL for Production)
spring.datasource.url=*************************************************
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:password}

# JPA Configuration for Production
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Logging for Production
logging.level.com.spring.fit.backend=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN 